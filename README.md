# Be-Finder Documentation

<div align="center">
  <h1>Be-Finder</h1>
  <h3>AI-Powered Location Intelligence Platform</h3>
  <p>Built on Internet Computer Protocol (ICP) using ICP Ninja IDE</p>
</div>

## Table of Contents
- [Overview](#overview)
- [Technical Stack](#technical-stack)
- [Project Structure](#project-structure)
- [Development Guide](#development-guide)
- [Core Features](#core-features)
- [Deployment](#deployment)
- [API Integration](#api-integration)
- [Troubleshooting](#troubleshooting)

## Overview

Be-Finder is a sophisticated location intelligence platform that combines advanced AI capabilities with blockchain technology to help businesses and individuals discover optimal locations for their needs. Built on the Internet Computer Protocol (ICP), it leverages cutting-edge AI models to provide:

- 🗺️ AI-driven location analysis and recommendations
- � Interactive mapping with real-time data visualization
- 💾 Decentralized file storage with chat session persistence
- 🤖 Intelligent location assistant powered by OpenRouter AI
- 📱 Responsive design optimized for all devices

## Technical Stack

- **Frontend**: React 18.3.1 with Vite 5.4.11
- **Backend**: <PERSON><PERSON><PERSON> on Internet Computer Protocol (ICP)
- **AI Models**: OpenRouter API with <PERSON>wen 3-235B
- **Styling**: Tailwind CSS 3.4.14 with custom dark theme
- **Mapping**: Leaflet 1.9.4 with OpenStreetMap integration
- **Authentication**: Internet Identity (II) for secure login
- **Development Environment**: ICP Ninja IDE compatible

## Project Structure

```text
/
├── backend/                           # Motoko canister code
│   └── app.mo                        # Main backend application with file storage
│
├── frontend/                         # React frontend application
│   ├── public/                      # Static assets
│   │   └── favicon.ico             # Application icon
│   │
│   ├── src/                        # Source code
│   │   ├── components/             # Reusable UI components
│   │   │   ├── ChatPanel.jsx       # AI chat interface
│   │   │   ├── Footer.jsx          # Footer component
│   │   │   ├── MainContent.jsx     # Main content area with tabs
│   │   │   ├── MobileHeader.jsx    # Mobile navigation header
│   │   │   ├── MobileSidebarOverlay.jsx # Mobile sidebar overlay
│   │   │   ├── Navbar.jsx          # Navigation bar component
│   │   │   ├── Sidebar.jsx         # Chat sessions sidebar
│   │   │   └── TabBar.jsx          # Tab navigation component
│   │   │
│   │   ├── pages/                 # Application pages
│   │   │   ├── App.jsx            # Main application dashboard
│   │   │   ├── Home.jsx           # Landing page with features
│   │   │   ├── About.jsx          # About page
│   │   │   ├── Contact.jsx        # Contact page
│   │   │   ├── Document.jsx       # Documentation page
│   │   │   ├── Features.jsx       # Features page
│   │   │   └── Pricing.jsx        # Pricing page
│   │   │
│   │   ├── services/              # API integrations
│   │   │   ├── Map.js             # Leaflet map utilities
│   │   │   └── openRouter.js      # OpenRouter AI integration
│   │   │
│   │   └── main.jsx               # Application entry point with routing
│   │
│   ├── index.html                # HTML entry point
│   ├── index.css                # Global styles with Tailwind
│   ├── tailwind.config.js       # Tailwind CSS configuration
│   ├── postcss.config.js        # PostCSS configuration
│   ├── vite.config.js           # Vite configuration
│   └── package.json             # Frontend dependencies
│
├── src/declarations/            # Generated canister interfaces
│   └── backend/                # Backend canister declarations
│       ├── backend.did         # Candid interface definition
│       ├── backend.did.d.ts    # TypeScript declarations
│       ├── backend.did.js      # JavaScript bindings
│       ├── index.d.ts          # TypeScript index
│       └── index.js            # JavaScript index
│
├── dfx.json                    # ICP canister configuration
├── mops.toml                   # Motoko package manager configuration
├── package.json                # Root package configuration
├── package-lock.json           # Dependency lock file
├── BUILD.md                    # Build instructions
└── README.md                   # Project documentation
```

### Key Directories

- `backend/`: Contains the Motoko canister code for secure file storage
- `frontend/src/components/`: Reusable React components for UI elements
- `frontend/src/pages/`: Application pages including main app and marketing pages
- `frontend/src/services/`: API integrations for mapping and AI functionality
- `src/declarations/`: Auto-generated canister interfaces for frontend integration

## Development Guide

### Prerequisites
- ICP Ninja IDE (recommended for quick start)
- Web Browser (Chrome/Firefox recommended)
- Internet Computer Identity (for deployment)

### Development Options

#### Option 1: ICP Ninja IDE (Recommended for Quick Start)
1. Open ICP Ninja IDE in your browser
2. Create a new project or import existing code
3. The IDE provides a temporary development environment valid for 20 minutes
4. Use the built-in deployment feature for testing

#### Option 2: Local Development with WSL (Windows)
1. Install Windows Subsystem for Linux (WSL)
   ```bash
   wsl --install
   ```

2. Install developer tools in WSL:
   ```bash
   curl -fsSL https://internetcomputer.org/install.sh | sh
   ```

3. Install Node.js and npm:
   ```bash
   curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
   sudo apt-get install -y nodejs
   ```

4. Install Motoko package manager:
   ```bash
   npm install -g ic-mops
   ```

5. Clone the repository and navigate to the project directory:
   ```bash
   git clone https://github.com/JBL-987/Be-Finder.git
   cd Be-Finder
   ```

6. Install dependencies:
   ```bash
   npm install
   ```

7. Start the local ICP replica:
   ```bash
   dfx start --background
   ```

8. Deploy the canisters:
   ```bash
   dfx deploy
   ```

#### Option 3: Native Installation (macOS/Linux)
1. Install `dfx` with the following command:
   ```bash
   sh -ci "$(curl -fsSL https://internetcomputer.org/install.sh)"
   ```

2. Install Node.js and npm
3. Install Motoko package manager: `npm install -g ic-mops`
4. Clone the repository and navigate to the project directory
5. Install dependencies: `npm install`
6. Start the local ICP replica: `dfx start --background`
7. Deploy the canisters: `dfx deploy`

## Core Features

### AI-Powered Location Intelligence
The application leverages OpenRouter AI to provide sophisticated location analysis:

```javascript
// AI Assistant Integration
const system_prompt = `You are Locatify's advanced AI assistant, a specialized location intelligence platform powered by cutting-edge technology and living on the Internet Computer blockchain. Your primary mission is to help users discover, analyze, and optimize strategic locations for their business needs and personal requirements.`;

const response = await fetch(OPENROUTER_API_URL, {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    "Authorization": `Bearer ${OPENROUTER_API_KEY}`,
    "HTTP-Referer": window.location.origin,
    "X-Title": "Locatify AI Assistant"
  },
  body: JSON.stringify({
    model: "qwen/qwen3-235b-a22b:free",
    messages: [
      { role: "system", content: system_prompt },
      ...messages
    ],
    temperature: 0.7,
    max_tokens: 1000
  })
});
```

### Interactive Mapping System
Advanced mapping capabilities with Leaflet integration:

- **Dynamic Map Interface**: Interactive maps with multiple data layers
- **Location Search**: Geocoding with OpenStreetMap Nominatim API
- **Marker Management**: Pin locations with custom icons and popups
- **Radius Analysis**: Visualize catchment areas and market reach
- **Real-time Updates**: Live map interactions and data visualization

### Decentralized File Storage
Secure blockchain-based storage system:

```motoko
// File Storage in Motoko
type File = {
  name : Text;
  chunks : [FileChunk];
  totalSize : Nat;
  fileType : Text;
};

public shared (msg) func uploadFileChunk(name : Text, chunk : Blob, index : Nat, fileType : Text) : async () {
  let userFiles = getUserFiles(msg.caller);
  let fileChunk = { chunk = chunk; index = index };
  // Store file chunks securely on ICP
};
```

### Chat Session Persistence
AI conversations are automatically saved to the blockchain:

- **Session Management**: Create, save, and retrieve chat sessions
- **Persistent Storage**: Chat history stored on ICP for long-term access
- **Search Functionality**: Find previous conversations by content
- **Cross-Device Sync**: Access conversations from any device

### Multi-Tab Interface
Organized workspace with specialized tools:

1. **Map Tab**: Interactive location mapping and analysis
2. **Analytics Tab**: Business analytics and market insights
3. **Reports Tab**: Generate comprehensive location reports
4. **Data Tab**: File management and document storage

## Deployment

### Local Deployment
1. Start the local ICP replica:
   ```bash
   dfx start --background
   ```

2. Deploy the canisters:
   ```bash
   dfx deploy
   ```

3. Start the frontend development server:
   ```bash
   npm run dev
   ```

4. The application will be available at: `http://localhost:5173`

### ICP Ninja IDE Deployment
1. Build & Deploy process through the IDE interface
2. Mainnet deployment steps as outlined in BUILD.md
3. Use the built-in deployment tools for quick testing

### Mainnet Deployment
1. Ensure you have cycles available for deployment
2. Deploy to the Internet Computer mainnet:
   ```bash
   dfx deploy --network ic
   ```

3. Your application will be accessible via the IC gateway

### Environment Configuration
Create a `.env` file in the root directory:
```env
VITE_REACT_OPENROUTER_API_KEY=your_openrouter_api_key_here
DFX_NETWORK=local  # or 'ic' for mainnet
CANISTER_ID_BACKEND=your_backend_canister_id
```

## API Integration

### OpenRouter AI Configuration
The application integrates with OpenRouter for AI-powered location analysis:

```javascript
// OpenRouter API Configuration
const OPENROUTER_API_KEY = import.meta.env.VITE_REACT_OPENROUTER_API_KEY;
const OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions";

// Specialized Location Intelligence Prompt
const system_prompt = `You are Locatify's advanced AI assistant, a specialized location intelligence platform...`;
```

### Mapping Services Integration
Leaflet with OpenStreetMap for mapping functionality:

```javascript
// Map Configuration
export const MapConfig = {
  defaultCenter: [-6.2088, 106.8456], // Jakarta, Indonesia
  defaultZoom: 13,
  tileLayer: {
    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    attribution: '© OpenStreetMap contributors'
  }
};

// Geocoding with Nominatim API
const geocodeLocation = async (query) => {
  const response = await fetch(
    `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}`
  );
  return await response.json();
};
```

### Internet Identity Authentication
Secure authentication using ICP's Internet Identity:

```javascript
// Authentication Setup
const authClient = await AuthClient.create();
const isAuthenticated = await authClient.isAuthenticated();

// Login Process
await authClient.login({
  identityProvider: "https://identity.ic0.app",
  maxTimeToLive: BigInt(7 * 24 * 60 * 60 * 1000 * 1000 * 1000), // 7 days
  onSuccess: () => {
    // Handle successful authentication
  }
});
```

## Troubleshooting

### Common Issues

1. **Canister Deployment Failed**
   - Check cycles balance for mainnet deployment
   - Verify canister settings in `dfx.json`
   - Ensure proper identity configuration: `dfx identity whoami`

2. **WSL Development Issues**
   - Ensure WSL is properly installed: `wsl --status`
   - Check if dfx is installed correctly: `dfx --version`
   - Verify Node.js installation: `node --version`
   - Make sure ports 4943 and 5173 are not in use

3. **Frontend Build Issues**
   - Clear node modules and reinstall: `rm -rf node_modules && npm install`
   - Check Vite configuration in `vite.config.js`
   - Verify environment variables are properly set

4. **API Integration Issues**
   - Verify OpenRouter API key is correctly set in `.env`
   - Check network connectivity and API quotas
   - Examine browser console for detailed error messages
   - Ensure CORS settings are properly configured

5. **Authentication Problems**
   - Clear browser cache and cookies
   - Check Internet Identity service status
   - Verify canister IDs are correctly configured
   - Ensure proper network configuration (local vs mainnet)

6. **Mapping Issues**
   - Check internet connectivity for tile loading
   - Verify Leaflet CSS is properly imported
   - Ensure map container has proper dimensions
   - Check browser console for JavaScript errors

7. **File Storage Issues**
   - Verify user is properly authenticated
   - Check file size limits (1MB chunks)
   - Ensure proper file type validation
   - Monitor canister memory usage

### Development Tips

- Use browser developer tools to debug frontend issues
- Check dfx logs for backend canister errors: `dfx logs`
- Test canister methods directly: `dfx canister call backend getFiles`
- Use ICP Ninja IDE for quick prototyping and testing
- Monitor network requests in browser dev tools for API issues

### Performance Optimization

- Implement proper error boundaries in React components
- Use React.memo for expensive component renders
- Optimize map rendering with proper zoom levels
- Implement proper loading states for better UX
- Use chunked file uploads for large files

### Security Considerations

- Never expose API keys in frontend code
- Validate all user inputs on both frontend and backend
- Implement proper authentication checks
- Use HTTPS in production environments
- Follow ICP security best practices for canister development

---

**For additional support, please refer to the [ICP Developer Documentation](https://internetcomputer.org/docs) or join the [DFINITY Developer Forum](https://forum.dfinity.org/)**
