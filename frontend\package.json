{"name": "frontend", "private": true, "type": "module", "scripts": {"prebuild": "npm i --include=dev && dfx generate backend", "build": "vite build", "dev": "vite"}, "dependencies": {"@dfinity/agent": "2.4.1", "@dfinity/auth-client": "2.4.1", "@dfinity/candid": "2.4.1", "@dfinity/principal": "2.4.1", "leaflet": "^1.9.4", "react": "18.3.1", "react-dom": "18.3.1", "sweetalert2": "^11.22.2"}, "devDependencies": {"@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@vitejs/plugin-react": "4.3.3", "autoprefixer": "^10.4.20", "postcss": "8.4.48", "tailwindcss": "3.4.14", "vite": "5.4.11", "vite-plugin-environment": "1.1.3"}}