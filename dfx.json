{"canisters": {"backend": {"main": "backend/app.mo", "type": "motoko", "args": "--enhanced-orthogonal-persistence"}, "frontend": {"dependencies": ["backend"], "frontend": {"entrypoint": "frontend/index.html"}, "source": ["frontend/dist"], "type": "assets"}, "internet_identity": {"candid": "https://github.com/dfinity/internet-identity/releases/latest/download/internet_identity.did", "type": "custom", "specified_id": "rdmx6-jaaaa-aaaaa-aaadq-cai", "remote": {"id": {"ic": "rdmx6-jaaaa-aaaaa-aaadq-cai"}}, "wasm": "https://github.com/dfinity/internet-identity/releases/latest/download/internet_identity_dev.wasm.gz"}}, "output_env_file": ".env", "defaults": {"build": {"packtool": "mops sources"}}}