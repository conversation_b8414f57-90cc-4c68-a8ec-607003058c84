service : {
  checkFileExists: (text) -> (bool);
  deleteFile: (text) -> (bool);
  getFileChunk: (text, nat) -> (opt blob);
  getFileType: (text) -> (opt text);
  getFiles: () -> (vec record {
                         fileType: text;
                         name: text;
                         size: nat;
                       });
  getTotalChunks: (text) -> (nat);
  uploadFileChunk: (text, blob, nat, text) -> ();
}
