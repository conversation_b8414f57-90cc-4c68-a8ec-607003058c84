Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFAE80) msys-2.0.dll+0x1FEBA
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210285FF9, 0007FFFFBE38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFBF80  0002100690B4 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFC260  00021006A49D (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFB3500000 ntdll.dll
7FFFB2130000 KERNEL32.DLL
7FFFB0EB0000 KERNELBASE.dll
7FFFB16D0000 USER32.dll
7FFFB0700000 win32u.dll
7FFFB1510000 GDI32.dll
7FFFB0C20000 gdi32full.dll
7FFFB0650000 msvcp_win.dll
7FFFB0AD0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFFB1FF0000 advapi32.dll
7FFFB2E10000 msvcrt.dll
7FFFB2450000 sechost.dll
7FFFB2330000 RPCRT4.dll
7FFFAFD70000 CRYPTBASE.DLL
7FFFB08B0000 bcryptPrimitives.dll
7FFFB1600000 IMM32.DLL
