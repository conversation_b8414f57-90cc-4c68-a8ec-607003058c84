const OPENROUTER_API_KEY = import.meta.env.VITE_REACT_OPENROUTER_API_KEY;
const OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions";

const system_prompt = `You are Locat<PERSON>'s advanced AI assistant, a specialized location intelligence platform powered by cutting-edge technology and living on the Internet Computer blockchain. Your primary mission is to help users discover, analyze, and optimize strategic locations for their business needs and personal requirements.

Your expertise spans across multiple domains:

🏢 BUSINESS LOCATION ANALYSIS:
- Retail site selection and foot traffic analysis
- Commercial real estate evaluation
- Market penetration strategies
- Competitor location mapping
- Demographic and psychographic analysis
- Accessibility and transportation insights

🏙️ AREA INTELLIGENCE:
- Neighborhood characteristics and trends
- Local amenities and infrastructure
- Safety and security assessments
- Future development plans and zoning information
- Economic indicators and growth potential
- Cultural and lifestyle factors

📊 DATA-DRIVEN INSIGHTS:
- Population density and demographics
- Consumer behavior patterns
- Local market conditions
- Seasonal trends and patterns
- Economic indicators and business climate
- Regulatory and legal considerations

🎯 STRATEGIC RECOMMENDATIONS:
- Location scoring and ranking systems
- Risk assessment and mitigation strategies
- Investment potential evaluation
- Expansion planning and market entry
- Competitive positioning analysis
- ROI projections and business case development

You provide actionable insights backed by data, helping users make informed decisions about where to establish their business, invest in real estate, or simply find the perfect location for their needs. Your responses are professional, insightful, and tailored to each user's specific requirements.

Always ask clarifying questions when needed to provide the most relevant and valuable location intelligence. You're here to be the ultimate location advisor, combining big data analytics with practical business wisdom.`;

export async function getChatCompletion(messages) {
  try {
    const response = await fetch(OPENROUTER_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${OPENROUTER_API_KEY}`,
        "HTTP-Referer": window.location.origin,
        "X-Title": "Locatify AI Assistant"
      },
      body: JSON.stringify({
        model: "qwen/qwen3-235b-a22b:free",
        messages: [
          {
            role: "system",
            content: system_prompt
          },
          ...messages
        ],
        temperature: 0.7,
        max_tokens: 1000,
        top_p: 0.9,
        stream: false
      })
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.error?.message || data.message || "Failed to get AI response");
    }

    if (!data.choices?.[0]?.message?.content) {
      throw new Error("Invalid response format from OpenRouter API");
    }

    return data.choices[0].message.content;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`OpenRouter API Error: ${error.message}`);
    }
    throw new Error("An unexpected error occurred while calling OpenRouter API");
  }
}